<template>
  <div class="header-wrapper fixed top-0 left-0 right-0 w-full flex justify-between items-center py-4 px-4 md:py-[24px] md:px-[120px] z-50 bg-[#0a0c12] bg-opacity-90 backdrop-blur">
    <NuxtLink to="/" class="logo font-['Bricolage_Grotesque'] text-[24px] md:text-[36px] font-semibold leading-[30px] md:leading-[44px] text-white tracking-[-0.72px]">
      Al-Alwi
    </NuxtLink>
    
    <!-- Mobile menu button -->
    <button @click="toggleMobileMenu" class="md:hidden text-white">
      <Icon v-if="!mobileMenuOpen" icon="heroicons:bars-3" class="w-6 h-6" />
      <Icon v-else icon="heroicons:x-mark" class="w-6 h-6" />
    </button>
    
    <!-- Desktop navigation -->
    <div class="nav-links hidden md:flex gap-[32px] items-center">
      <NuxtLink v-for="link in navLinks" :key="link.path" :to="link.path" 
            class="nav-link font-['Lato'] text-[16px] font-normal leading-[28px] text-[#a3a7ae] hover:text-white transition-colors"
            :class="{ 'text-white': isActive(link.path) }">
        {{ link.label }}
      </NuxtLink>
    </div>
    
    <!-- Desktop actions -->
    <div class="actions hidden md:flex gap-[24px] items-center">
      <div class="language-selector flex gap-[8px] items-center cursor-pointer" @click="toggleLanguage">
        <Icon icon="heroicons:language" class="w-[16px] h-[16px] text-white" />
        <span class="font-['Noto_Sans'] text-[16px] font-normal leading-[24px] text-white">{{ currentLocale.toUpperCase() }}</span>
        <Icon icon="heroicons:chevron-down" class="w-[12px] h-[12px] text-white" />
      </div>
      
      <Button 
        as="NuxtLink" 
        to="/contact" 
        variant="primary"
      >
        <span class="font-['Lato'] font-medium leading-[24px]">Book A Call</span>
      </Button>
    </div>
    
    <!-- Mobile menu -->
    <div v-if="mobileMenuOpen" class="mobile-menu fixed inset-0 bg-[#0a0c12] z-40 flex flex-col items-center justify-center">
      <div class="flex flex-col items-center gap-8">
        <NuxtLink 
          v-for="link in navLinks" 
          :key="link.path" 
          :to="link.path" 
          @click="mobileMenuOpen = false"
          class="nav-link font-['Lato'] text-[24px] font-normal leading-[34px] text-[#a3a7ae] hover:text-white transition-colors"
          :class="{ 'text-white': isActive(link.path) }"
        >
          {{ link.label }}
        </NuxtLink>
        
        <div class="flex flex-col items-center gap-4 mt-8">
          <div class="language-selector flex gap-[8px] items-center cursor-pointer" @click="toggleLanguage">
            <Icon icon="heroicons:language" class="w-[16px] h-[16px] text-white" />
            <span class="font-['Noto_Sans'] text-[16px] font-normal leading-[24px] text-white">{{ currentLocale.toUpperCase() }}</span>
            <Icon icon="heroicons:chevron-down" class="w-[12px] h-[12px] text-white" />
          </div>
          
          <Button 
            as="NuxtLink" 
            to="/contact" 
            variant="primary"
            @click="mobileMenuOpen = false"
          >
            <span class="font-['Lato'] font-medium leading-[24px]">Book A Call</span>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useLanguage } from '~/utils/language';
import { useNavigation } from '~/utils/navigation';
import Button from '~/components/ui/Button.vue';

const { currentLocale, toggleLanguage } = useLanguage();
const { navLinks, isActive } = useNavigation();
const mobileMenuOpen = ref(false);

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
  if (mobileMenuOpen.value) {
    document.body.classList.add('overflow-hidden');
  } else {
    document.body.classList.remove('overflow-hidden');
  }
};
</script>

<style scoped>
.header-wrapper {
  transition: background-color 0.3s ease;
}

.mobile-menu {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

body.overflow-hidden {
  overflow: hidden;
}
</style> 